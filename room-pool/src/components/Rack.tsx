"use client"
import clsx from "clsx"
import type { Rack<PERSON><PERSON> } from "@/lib/types"

// Pool ball colors and styles
const getBallStyle = (ballId: number, pocketed: boolean) => {
  const baseClasses = "aspect-square rounded-full flex items-center justify-center text-xl font-bold border-2 relative overflow-hidden"

  if (pocketed) {
    return {
      className: clsx(baseClasses, "bg-gray-400 text-gray-600 border-gray-500 opacity-50"),
      style: {}
    }
  }

  // Ball color definitions
  const ballStyles: Record<number, { bg: string, text: string, border: string, isStripe?: boolean }> = {
    1: { bg: "bg-yellow-400", text: "text-black", border: "border-yellow-600" },
    2: { bg: "bg-blue-600", text: "text-white", border: "border-blue-800" },
    3: { bg: "bg-red-600", text: "text-white", border: "border-red-800" },
    4: { bg: "bg-purple-600", text: "text-white", border: "border-purple-800" },
    5: { bg: "bg-orange-500", text: "text-white", border: "border-orange-700" },
    6: { bg: "bg-green-600", text: "text-white", border: "border-green-800" },
    7: { bg: "bg-red-900", text: "text-white", border: "border-red-950" },
    8: { bg: "bg-black", text: "text-white", border: "border-gray-800" },
    9: { bg: "bg-yellow-400", text: "text-black", border: "border-yellow-600", isStripe: true },
    10: { bg: "bg-blue-600", text: "text-white", border: "border-blue-800", isStripe: true },
    11: { bg: "bg-red-600", text: "text-white", border: "border-red-800", isStripe: true },
    12: { bg: "bg-purple-600", text: "text-white", border: "border-purple-800", isStripe: true },
    13: { bg: "bg-orange-500", text: "text-white", border: "border-orange-700", isStripe: true },
    14: { bg: "bg-green-600", text: "text-white", border: "border-green-800", isStripe: true },
    15: { bg: "bg-red-900", text: "text-white", border: "border-red-950", isStripe: true }
  }

  const style = ballStyles[ballId] || { bg: "bg-white", text: "text-black", border: "border-gray-300" }

  return {
    className: clsx(baseClasses, style.bg, style.text, style.border),
    isStripe: style.isStripe,
    style: {}
  }
}

// Triangle formation positions - standard 8-ball rack
const getTrianglePosition = (ballId: number) => {
  // Proper 8-ball rack formation:
  // Row 0: 1 ball (apex)
  // Row 1: 2 balls
  // Row 2: 3 balls (8-ball in center)
  // Row 3: 4 balls
  // Row 4: 5 balls (base)
  const positions: Record<number, { row: number, col: number }> = {
    1: { row: 0, col: 2 },   // Apex (1-ball at front)
    2: { row: 1, col: 1 },   // Second row left
    3: { row: 1, col: 3 },   // Second row right
    4: { row: 2, col: 0 },   // Third row left
    8: { row: 2, col: 2 },   // Third row center (8-ball)
    5: { row: 2, col: 4 },   // Third row right
    6: { row: 3, col: 1 },   // Fourth row left-center
    7: { row: 3, col: 3 },   // Fourth row right-center
    9: { row: 4, col: 0 },   // Fifth row leftmost
    10: { row: 4, col: 1 },  // Fifth row left-center
    11: { row: 4, col: 2 },  // Fifth row center
    12: { row: 4, col: 3 },  // Fifth row right-center
    13: { row: 4, col: 4 },  // Fifth row rightmost
    14: { row: 3, col: 1 },  // Fourth row (adjust for proper spacing)
    15: { row: 3, col: 3 }   // Fourth row (adjust for proper spacing)
  }

  // Fix overlapping positions
  const finalPositions: Record<number, { row: number, col: number }> = {
    1: { row: 0, col: 2 },   // Apex
    2: { row: 1, col: 1 },   // Row 2 left
    3: { row: 1, col: 3 },   // Row 2 right
    4: { row: 2, col: 0 },   // Row 3 left
    8: { row: 2, col: 2 },   // Row 3 center (8-ball)
    5: { row: 2, col: 4 },   // Row 3 right
    6: { row: 3, col: 1 },   // Row 4 left
    7: { row: 3, col: 3 },   // Row 4 right
    9: { row: 4, col: 0 },   // Row 5 leftmost
    10: { row: 4, col: 1 },  // Row 5 left-center
    11: { row: 4, col: 2 },  // Row 5 center
    12: { row: 4, col: 3 },  // Row 5 right-center
    13: { row: 4, col: 4 },  // Row 5 rightmost
    14: { row: 3, col: 0 },  // Row 4 leftmost (was overlapping)
    15: { row: 3, col: 4 }   // Row 4 rightmost (was overlapping)
  }

  return finalPositions[ballId] || { row: 0, col: 0 }
}

export default function Rack({ rack, onToggle, interactive }:{
  rack: RackBall[]
  onToggle?:(id:number)=>void
  interactive?: boolean
}){
  // Create a 5x5 grid to hold the triangle formation
  const triangleGrid = Array(5).fill(null).map(() => Array(5).fill(null))

  // Place balls in triangle formation
  rack.forEach(ball => {
    const pos = getTrianglePosition(ball.id)
    triangleGrid[pos.row][pos.col] = ball
  })

  return (
    <div className="flex flex-col items-center gap-1 max-w-md w-full">
      {triangleGrid.map((row, rowIndex) => (
        <div key={rowIndex} className="flex gap-1" style={{
          marginLeft: `${(4 - rowIndex) * 0.75}rem` // Better spacing for triangle shape
        }}>
          {row.map((ball, colIndex) => {
            if (!ball) return <div key={colIndex} className="w-12 h-12" /> // Empty space

            const ballStyle = getBallStyle(ball.id, ball.pocketed)

            return (
              <button
                key={ball.id}
                onClick={() => interactive && onToggle?.(ball.id)}
                className={clsx(
                  ballStyle.className,
                  "w-12 h-12 text-sm font-bold shadow-lg transition-all duration-200",
                  interactive ? "hover:scale-110 hover:shadow-xl cursor-pointer" : "cursor-default",
                  ball.pocketed && "scale-90"
                )}
                aria-label={`ball-${ball.id}`}
                disabled={!interactive}
                title={ball.by ? `Pocketed by ${ball.by}` : `Ball ${ball.id}`}
              >
                {/* Single stripe line for striped balls */}
                {ballStyle.isStripe && !ball.pocketed && (
                  <div className="absolute inset-0 rounded-full overflow-hidden">
                    <div className="absolute top-1/2 left-0 right-0 h-1 bg-white transform -translate-y-1/2" />
                  </div>
                )}
                <span className="relative z-10">{ball.id}</span>
              </button>
            )
          })}
        </div>
      ))}
    </div>
  )
}
