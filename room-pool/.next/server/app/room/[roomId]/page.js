/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/room/[roomId]/page";
exports.ids = ["app/room/[roomId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?d272":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'room',\n        {\n        children: [\n        '[roomId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/room/[roomId]/page.tsx */ \"(rsc)/./src/app/room/[roomId]/page.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/room/[roomId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/room/[roomId]/page\",\n        pathname: \"/room/[roomId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp%2Froom%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp%2Froom%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/room/[roomId]/page.tsx */ \"(ssr)/./src/app/room/[roomId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbnVsbCUyRkRvY3VtZW50cyUyRkRldmVsb3BtZW50JTJGU2VwdC0yMDI1JTJGYmlsbGlhcmRzJTJGcm9vbS1wb29sJTJGc3JjJTJGYXBwJTJGcm9vbSUyRiU1QnJvb21JZCU1RCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBbUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yb29tLXBvb2wvPzQyZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbnVsbC9Eb2N1bWVudHMvRGV2ZWxvcG1lbnQvU2VwdC0yMDI1L2JpbGxpYXJkcy9yb29tLXBvb2wvc3JjL2FwcC9yb29tL1tyb29tSWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp%2Froom%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/room/[roomId]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/room/[roomId]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoomView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_Rack__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Rack */ \"(ssr)/./src/components/Rack.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction RoomView({ params }) {\n    const { roomId } = params;\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"spectator\");\n    const sock = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ]\n        });\n        sock.current = s;\n        const joinNow = ()=>{\n            if (joined && name.trim()) s.emit(\"room:join\", {\n                roomId,\n                name: name.trim()\n            });\n        };\n        s.on(\"connect\", ()=>{\n            joinNow();\n            s.emit(\"room:get\", {\n                roomId\n            });\n        });\n        s.on(\"room:state\", (st)=>setState(st));\n        s.on(\"room:role\", (r)=>setRole(r));\n        s.on(\"room:role:error\", (e)=>alert(`Seat ${e.role} taken`));\n        return ()=>{\n            s.disconnect();\n        };\n    }, [\n        joined,\n        name,\n        roomId\n    ]);\n    const requestRole = (r)=>sock.current?.emit(\"role:request\", {\n            roomId,\n            role: r\n        });\n    const joinQueue = ()=>sock.current?.emit(\"queue:join\", {\n            roomId\n        });\n    const leaveQueue = ()=>sock.current?.emit(\"queue:leave\", {\n            roomId\n        });\n    const toggleBall = (id)=>sock.current?.emit(\"rack:toggle\", {\n            roomId,\n            ballId: id\n        });\n    const resetRack = ()=>sock.current?.emit(\"game:reset\", {\n            roomId\n        });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto max-w-md space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: [\n                    \"Room \",\n                    roomId\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            !joined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-3 rounded-2xl bg-white/10 p-4\",\n                onSubmit: (e)=>{\n                    e.preventDefault();\n                    if (!name.trim()) return;\n                    setJoined(true);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm opacity-80\",\n                        children: \"Display name\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: \"w-full rounded-lg bg-white/10 px-3 py-2 outline-none ring-1 ring-white/20\",\n                        value: name,\n                        onChange: (e)=>setName(e.target.value),\n                        placeholder: \"Your name\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full rounded-xl bg-brand-500 px-4 py-2 font-semibold hover:bg-brand-600\",\n                        children: \"Join\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-2xl bg-white/10 p-4 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"You are: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: role.toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 27\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>requestRole(\"P1\"),\n                                        disabled: !!state?.players.P1 && state.players.P1.id !== sock.current?.id,\n                                        className: \"rounded-lg bg-white/20 px-3 py-2 disabled:opacity-50\",\n                                        children: [\n                                            \"Be P1 \",\n                                            state?.players.P1?.name ? `(${state.players.P1.name})` : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>requestRole(\"P2\"),\n                                        disabled: !!state?.players.P2 && state.players.P2.id !== sock.current?.id,\n                                        className: \"rounded-lg bg-white/20 px-3 py-2 disabled:opacity-50\",\n                                        children: [\n                                            \"Be P2 \",\n                                            state?.players.P2?.name ? `(${state.players.P2.name})` : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>requestRole(\"spectator\"),\n                                        className: \"rounded-lg bg-white/20 px-3 py-2\",\n                                        children: \"Spectate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: joinQueue,\n                                        className: \"rounded-lg bg-white/20 px-3 py-2\",\n                                        children: \"Join Queue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: leaveQueue,\n                                        className: \"rounded-lg bg-white/20 px-3 py-2\",\n                                        children: \"Leave Queue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-2xl bg-white/10 p-4 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Rack__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            rack: state?.rack ?? [],\n                            interactive: role !== \"spectator\",\n                            onToggle: toggleBall\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-2xl bg-white/10 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Groups — P1: \",\n                                    state?.groups.P1 ?? \"?\",\n                                    \", P2: \",\n                                    state?.groups.P2 ?? \"?\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"opacity-80 text-sm\",\n                                children: \"Tap balls to mark pocketed. First non-8 ball sets groups. (Simplified 8-ball logic.)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetRack,\n                                    className: \"rounded-lg bg-white/20 px-3 py-2\",\n                                    children: \"Reset Rack\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 35\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/room/[roomId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Rack.tsx":
/*!*********************************!*\
  !*** ./src/components/Rack.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Rack)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Rack({ rack, onToggle, interactive }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-5 gap-3 max-w-md w-full\",\n        children: rack.map((b)=>{\n            const clicked = !!b.pocketed;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>interactive && onToggle?.(b.id),\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"aspect-square rounded-full flex items-center justify-center text-xl font-bold border\", clicked ? \"bg-white/70 text-black border-transparent\" : \"bg-white/10 border-white/20\", interactive ? \"hover:bg-white/20\" : \"cursor-default\"),\n                \"aria-label\": `ball-${b.id}`,\n                disabled: !interactive,\n                title: b.by ? `by ${b.by}` : \"\",\n                children: b.id\n            }, b.id, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/components/Rack.tsx\",\n                lineNumber: 15,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/components/Rack.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Rack.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a32642c513f1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcm9vbS1wb29sLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9jMTYwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTMyNjQyYzUxM2YxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Pool Room\",\n    description: \"TV server + phone participants with queue and virtual rack\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-br from-brand-800 via-brand-600 to-brand-500 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-6xl p-6\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/layout.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/layout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNCO0FBR2YsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFDYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBZ0M7SUFDM0UscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7c0JBQ2QsNEVBQUNDO2dCQUFJRCxXQUFVOzBCQUF5Qko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yb29tLXBvb2wvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIlBvb2wgUm9vbVwiLFxuICBkZXNjcmlwdGlvbjogXCJUViBzZXJ2ZXIgKyBwaG9uZSBwYXJ0aWNpcGFudHMgd2l0aCBxdWV1ZSBhbmQgdmlydHVhbCByYWNrXCJcbn1cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OnsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KXtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJyYW5kLTgwMCB2aWEtYnJhbmQtNjAwIHRvLWJyYW5kLTUwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy02eGwgcC02XCI+e2NoaWxkcmVufTwvZGl2PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/room/[roomId]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/room/[roomId]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/Sept-2025/billiards/room-pool/src/app/room/[roomId]/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Fbilliards%2Froom-pool&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();